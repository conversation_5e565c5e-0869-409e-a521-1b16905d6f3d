import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';
import { InvitationStatus } from '@prisma/client';

interface JWTInvitationData {
  masterEmail: string;
  childEmail: string;
  masterId: string;
  type: string;
}

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { valid: false, error: 'Token is required' },
        { status: 400 }
      );
    }

    // First, try to validate as a database token (new system)
    try {
      const invitation = await prisma.invitation.findUnique({
        where: { token },
        include: {
          sender: true,
        },
      });

      if (invitation) {
        // Check if invitation is valid
        if (invitation.status !== InvitationStatus.PENDING) {
          return NextResponse.json({
            valid: false,
            error: 'Invitation has already been processed'
          });
        }

        if (invitation.expiresAt < new Date()) {
          return NextResponse.json({
            valid: false,
            error: 'Invitation has expired'
          });
        }

        // Return invitation data in the expected format
        return NextResponse.json({
          valid: true,
          invitationData: {
            masterEmail: invitation.sender.email,
            childEmail: invitation.receiverEmail,
            masterId: invitation.senderId,
            type: 'invitation'
          },
          tokenType: 'database'
        });
      }
    } catch (dbError) {
      console.error('Database token validation error:', dbError);
      // Continue to JWT validation
    }

    // If not found in database, try to validate as JWT token (old system)
    try {
      const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
      const decoded = jwt.verify(token, JWT_SECRET) as JWTInvitationData;

      if (!decoded || decoded.type !== 'invitation') {
        return NextResponse.json({
          valid: false,
          error: 'Invalid token format'
        });
      }

      return NextResponse.json({
        valid: true,
        invitationData: {
          masterEmail: decoded.masterEmail,
          childEmail: decoded.childEmail,
          masterId: decoded.masterId,
          type: decoded.type
        },
        tokenType: 'jwt'
      });
    } catch (jwtError) {
      console.error('JWT token validation error:', jwtError);
      return NextResponse.json({
        valid: false,
        error: 'Invalid or expired invitation token'
      });
    }

  } catch (error) {
    console.error('Token validation error:', error);
    return NextResponse.json(
      { valid: false, error: 'Failed to validate token' },
      { status: 500 }
    );
  }
}
